#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 最小测试程序启动...")

try:
    import numpy as np
    import csv
    import time
    from collections import Counter, deque
    import os
    print("✅ 基础库导入成功")
except Exception as e:
    print(f"❌ 基础库导入失败: {e}")
    exit(1)

try:
    import tensorflow as tf
    print(f"✅ TensorFlow 导入成功，版本: {tf.__version__}")
except Exception as e:
    print(f"❌ TensorFlow 导入失败: {e}")
    exit(1)

try:
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    import tensorflow.keras.backend as K
    from tensorflow.keras.losses import binary_crossentropy
    print("✅ Keras 组件导入成功")
except Exception as e:
    print(f"❌ Keras 组件导入失败: {e}")
    exit(1)

# 全局设置
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3
NUM_SIMULATION_ATTEMPTS = 10
MAX_MODEL_RETRAIN_ATTEMPTS = 5
TARGET_HIT_RATE = 0.1

def load_all_draws(filename="lottery_results_formatted.csv"):
    print(f"📂 加载数据文件: {filename}")
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"❌ 文件 '{filename}' 未找到")
        return []
    except Exception as e:
        print(f"❌ 读取文件出错: {e}")
        return []
    
    print(f"✅ 成功加载 {len(all_draws)} 期数据")
    return all_draws

def create_simple_model(input_shape, output_shape):
    """创建简单模型用于测试"""
    print(f"🧠 创建模型: 输入{input_shape}, 输出{output_shape}")
    model = Sequential([
        Dense(64, activation='relu', input_shape=(input_shape,)),
        Dropout(0.3),
        Dense(32, activation='relu'),
        Dropout(0.3),
        Dense(output_shape, activation='sigmoid')
    ])
    model.compile(optimizer=Adam(learning_rate=0.001), loss='binary_crossentropy', metrics=['accuracy'])
    print("✅ 模型创建成功")
    return model

def create_simple_dataset(all_draws, red_range, blue_range):
    """创建简化数据集"""
    print(f"📊 创建简化数据集...")
    X_red, Y_red, X_blue, Y_blue = [], [], [], []
    
    # 只使用最近50期数据进行快速测试
    start_idx = max(50, len(all_draws) - 100)
    
    for i in range(start_idx, len(all_draws)):
        if i % 10 == 0:
            print(f"   处理第 {i} 期")
        
        # 简化特征：只使用基本统计
        target_draw = all_draws[i]
        
        # 红球特征：简单的频率统计
        red_features = []
        for num in red_range:
            # 计算在最近20期中的出现次数
            recent_count = 0
            for j in range(max(0, i-20), i):
                if num in all_draws[j]['red']:
                    recent_count += 1
            red_features.append(recent_count / 20)  # 归一化
        
        X_red.append(red_features)
        Y_red.append([1 if num in target_draw['red'] else 0 for num in red_range])
        
        # 蓝球特征：简单的频率统计
        blue_features = []
        for num in blue_range:
            recent_count = 0
            for j in range(max(0, i-20), i):
                if num in all_draws[j]['blue']:
                    recent_count += 1
            blue_features.append(recent_count / 20)  # 归一化
        
        X_blue.append(blue_features)
        Y_blue.append([1 if num in target_draw['blue'] else 0 for num in blue_range])
    
    print(f"✅ 简化数据集创建完成")
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)

if __name__ == "__main__":
    print("=" * 60)
    print("🎲 最小彩票预测程序")
    print("=" * 60)
    
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)
    
    # 加载数据
    all_draws = load_all_draws()
    if len(all_draws) < 50:
        print("❌ 数据不足")
        exit(1)
    
    # 创建数据集
    X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_simple_dataset(
        all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
    )
    
    print(f"📊 数据集形状:")
    print(f"   红球: {X_red_all.shape} -> {Y_red_all.shape}")
    print(f"   蓝球: {X_blue_all.shape} -> {Y_blue_all.shape}")
    
    # 分割数据
    split_idx = -3
    X_red_train, Y_red_train = X_red_all[:split_idx], Y_red_all[:split_idx]
    X_blue_train, Y_blue_train = X_blue_all[:split_idx], Y_blue_all[:split_idx]
    X_red_test, Y_red_test = X_red_all[split_idx:], Y_red_all[split_idx:]
    X_blue_test, Y_blue_test = X_blue_all[split_idx:], Y_blue_all[split_idx:]
    
    print(f"📊 训练集: 红球{X_red_train.shape}, 蓝球{X_blue_train.shape}")
    print(f"📊 测试集: 红球{X_red_test.shape}, 蓝球{X_blue_test.shape}")
    
    # 创建和训练模型
    print("\n🧠 开始训练模型...")
    red_model = create_simple_model(X_red_train.shape[1], Y_red_train.shape[1])
    blue_model = create_simple_model(X_blue_train.shape[1], Y_blue_train.shape[1])
    
    print("🔴 训练红球模型...")
    red_model.fit(X_red_train, Y_red_train, epochs=10, batch_size=16, verbose=1, validation_split=0.2)
    
    print("🔵 训练蓝球模型...")
    blue_model.fit(X_blue_train, Y_blue_train, epochs=10, batch_size=16, verbose=1, validation_split=0.2)
    
    print("✅ 训练完成！")
    print(f"\n总耗时: {(time.time() - start_time):.2f} 秒")
    print("程序结束。")
