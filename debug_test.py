#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 调试程序启动...")

try:
    import numpy as np
    print("✅ numpy 导入成功")
except ImportError as e:
    print(f"❌ numpy 导入失败: {e}")

try:
    import csv
    print("✅ csv 导入成功")
except ImportError as e:
    print(f"❌ csv 导入失败: {e}")

try:
    import time
    print("✅ time 导入成功")
except ImportError as e:
    print(f"❌ time 导入失败: {e}")

try:
    from collections import Counter, deque
    print("✅ collections 导入成功")
except ImportError as e:
    print(f"❌ collections 导入失败: {e}")

try:
    import os
    print("✅ os 导入成功")
except ImportError as e:
    print(f"❌ os 导入失败: {e}")

try:
    import tensorflow as tf
    print(f"✅ tensorflow 导入成功，版本: {tf.__version__}")
except ImportError as e:
    print(f"❌ tensorflow 导入失败: {e}")

try:
    from tensorflow.keras.models import Sequential, load_model
    from tensorflow.keras.layers import Dense, Dropout
    from tensorflow.keras.optimizers import Adam
    from tensorflow.keras.callbacks import EarlyStopping
    import tensorflow.keras.backend as K
    from tensorflow.keras.losses import binary_crossentropy
    print("✅ tensorflow.keras 组件导入成功")
except ImportError as e:
    print(f"❌ tensorflow.keras 组件导入失败: {e}")

# 测试数据加载
print("\n📂 测试数据加载...")
try:
    def load_all_draws(filename="lottery_results_formatted.csv"):
        all_draws = []
        try:
            with open(filename, 'r', encoding='utf-8-sig') as f:
                reader = csv.reader(f)
                next(reader)  # 跳过表头
                for row in reader:
                    parts = row[1].split(' | ')
                    if len(parts) == 2 and parts[0] and parts[1]:
                        draw = {
                            "date": row[0],
                            "red": [int(n) for n in parts[0].split('-')],
                            "blue": [int(n) for n in parts[1].split('-')]
                        }
                        all_draws.append(draw)
        except FileNotFoundError:
            print(f"错误: 文件 '{filename}' 未找到。")
            return []
        return all_draws
    
    all_draws = load_all_draws()
    print(f"✅ 成功加载 {len(all_draws)} 期历史数据")
    if len(all_draws) > 0:
        print(f"📊 最新一期: {all_draws[-1]}")
        print(f"📊 最早一期: {all_draws[0]}")
    
except Exception as e:
    print(f"❌ 数据加载失败: {e}")

print("\n🎯 调试完成！")
