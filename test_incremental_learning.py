#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量学习测试脚本
用于验证增量学习功能是否正常工作
"""

import os
import json
import numpy as np
from datetime import datetime

def test_training_history():
    """测试训练历史记录功能"""
    print("🧪 测试训练历史记录功能...")
    
    # 创建测试历史数据
    test_history = {
        "iterations": [
            {
                "iteration": 1,
                "hit_rate": 0.05,
                "learning_rate": 0.0005,
                "timestamp": datetime.now().isoformat(),
                "total_hits": 1,
                "total_tests": 3
            },
            {
                "iteration": 2,
                "hit_rate": 0.08,
                "learning_rate": 0.0004,
                "timestamp": datetime.now().isoformat(),
                "total_hits": 2,
                "total_tests": 3
            }
        ],
        "best_hit_rate": 0.08,
        "best_iteration": 2,
        "total_iterations": 2
    }
    
    # 保存测试历史
    with open("test_training_history.json", 'w', encoding='utf-8') as f:
        json.dump(test_history, f, indent=2, ensure_ascii=False)
    
    # 读取并验证
    with open("test_training_history.json", 'r', encoding='utf-8') as f:
        loaded_history = json.load(f)
    
    assert loaded_history["best_hit_rate"] == 0.08
    assert loaded_history["total_iterations"] == 2
    print("✅ 训练历史记录功能测试通过")
    
    # 清理测试文件
    os.remove("test_training_history.json")

def test_learning_rate_adaptation():
    """测试学习率自适应调整"""
    print("🧪 测试学习率自适应调整...")
    
    # 模拟学习率调整函数
    def get_adaptive_learning_rate(current_hit_rate, best_hit_rate, base_lr):
        MIN_LEARNING_RATE = 0.00001
        if current_hit_rate >= best_hit_rate:
            return max(base_lr * 0.9, MIN_LEARNING_RATE)
        else:
            return max(base_lr * 0.5, MIN_LEARNING_RATE)
    
    # 测试性能提升情况
    new_lr = get_adaptive_learning_rate(0.1, 0.08, 0.0005)
    expected_lr = max(0.0005 * 0.9, 0.00001)
    assert abs(new_lr - expected_lr) < 1e-6, f"期望 {expected_lr}, 得到 {new_lr}"
    
    # 测试性能下降情况
    new_lr = get_adaptive_learning_rate(0.06, 0.08, 0.0005)
    expected_lr = max(0.0005 * 0.5, 0.00001)
    assert abs(new_lr - expected_lr) < 1e-6, f"期望 {expected_lr}, 得到 {new_lr}"
    
    print("✅ 学习率自适应调整测试通过")

def test_model_backup_naming():
    """测试模型备份命名规则"""
    print("🧪 测试模型备份命名规则...")
    
    iteration = 5
    hit_rate = 0.123
    timestamp = "20241216_143022"
    
    expected_red_name = f"red_model_iter{iteration}_{timestamp}_rate{hit_rate:.3f}.keras"
    expected_blue_name = f"blue_model_iter{iteration}_{timestamp}_rate{hit_rate:.3f}.keras"
    
    assert expected_red_name == "red_model_iter5_20241216_143022_rate0.123.keras"
    assert expected_blue_name == "blue_model_iter5_20241216_143022_rate0.123.keras"
    
    print("✅ 模型备份命名规则测试通过")

def test_configuration_values():
    """测试配置值的合理性"""
    print("🧪 测试配置值...")
    
    # 模拟配置值
    INITIAL_LEARNING_RATE = 0.0005
    MIN_LEARNING_RATE = 0.00001
    INCREMENTAL_EPOCHS = 5
    PATIENCE_EPOCHS = 3
    
    assert INITIAL_LEARNING_RATE > MIN_LEARNING_RATE, "初始学习率应大于最小学习率"
    assert INCREMENTAL_EPOCHS > 0, "增量训练轮数应大于0"
    assert PATIENCE_EPOCHS > 0, "早停耐心值应大于0"
    assert PATIENCE_EPOCHS < INCREMENTAL_EPOCHS, "早停耐心值应小于训练轮数"
    
    print("✅ 配置值测试通过")

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始增量学习功能测试...")
    print("=" * 50)
    
    try:
        test_training_history()
        test_learning_rate_adaptation()
        test_model_backup_naming()
        test_configuration_values()
        
        print("=" * 50)
        print("🎉 所有测试通过！增量学习功能正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = run_all_tests()
    if success:
        print("\n✅ 增量学习功能验证完成，可以运行主程序进行实际训练。")
    else:
        print("\n❌ 测试失败，请检查代码。")
