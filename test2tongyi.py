import numpy as np
import csv
import time
from collections import Counter, deque
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping
import tensorflow.keras.backend as K
from tensorflow.keras.losses import binary_crossentropy

# --- 全局设置 ---
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3
NUM_SIMULATION_ATTEMPTS = 10
MAX_MODEL_RETRAIN_ATTEMPTS = 40  # 最大重训练次数
TARGET_HIT_RATE = 0.1  # 目标命中率（10次内至少命中1次）


# --- 所有函数定义 (与之前版本相同) ---

# 1. 数据加载
def load_all_draws(filename="lottery_results_formatted.csv"):
    # ... 此函数未改变 ...
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。请确保CSV文件在同一目录下。")
        return []
    return all_draws


# 2. 独立号码特征工程
def calculate_individual_features(history_draws, red_range, blue_range):
    """计算个体特征 - 优化版本"""
    red_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0,
        'positional_frequency': [0] * 5,
    } for num in red_range}
    blue_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0
    } for num in blue_range}

    if not history_draws:
        return red_features, blue_features

    # 优化：只计算最后出现位置
    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}

    # 反向遍历，找到最后出现位置
    for i in range(len(history_draws) - 1, -1, -1):
        draw = history_draws[i]
        for num in draw['red']:
            if last_seen_red[num] == -1:
                last_seen_red[num] = i
        for num in draw['blue']:
            if last_seen_blue[num] == -1:
                last_seen_blue[num] = i

    total_draws = len(history_draws)
    for num in red_range:
        red_features[num]['omission'] = total_draws - 1 - last_seen_red[num]
    for num in blue_range:
        blue_features[num]['omission'] = total_draws - 1 - last_seen_blue[num]

    # 只使用最近的窗口数据
    windowed_history = history_draws[-FREQUENCY_WINDOW:]
    red_counts, blue_counts = Counter(), Counter()
    positional_counts = {num: [0] * 5 for num in red_range}

    # 简化热度计算
    for draw in windowed_history:
        sorted_red = sorted(draw['red'])
        for idx, num in enumerate(sorted_red):
            positional_counts[num][idx] += 1

        # 直接计数，不用复杂的热度公式
        for num in draw['red']:
            red_counts[num] += 1
        for num in draw['blue']:
            blue_counts[num] += 1

    # 设置特征值
    for num in red_range:
        red_features[num]['frequency'] = red_counts[num]
        red_features[num]['heat'] = red_counts[num] / len(windowed_history)  # 简化热度
        red_features[num]['positional_frequency'] = positional_counts[num]

    for num in blue_range:
        blue_features[num]['frequency'] = blue_counts[num]
        blue_features[num]['heat'] = blue_counts[num] / len(windowed_history)  # 简化热度

    return red_features, blue_features


# 3. 融合宏观特征的数据集创建
def create_composite_dataset(all_draws, red_range, blue_range):
    """创建复合数据集 - 优化版本"""
    print(f"📊 开始创建数据集，总数据: {len(all_draws)} 期")
    X_red, Y_red, X_blue, Y_blue = [], [], [], []

    total_samples = len(all_draws) - FREQUENCY_WINDOW
    print(f"📊 将生成 {total_samples} 个训练样本")

    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        # 显示进度
        if (i - FREQUENCY_WINDOW) % 20 == 0:
            progress = (i - FREQUENCY_WINDOW + 1) / total_samples * 100
            print(f"   进度: {progress:.1f}% ({i - FREQUENCY_WINDOW + 1}/{total_samples})")

        history_for_features = all_draws[:i]
        target_draw, prev_draw = all_draws[i], all_draws[i - 1]
        red_features, blue_features = calculate_individual_features(history_for_features, red_range, blue_range)

        # 构建红球特征向量
        red_feature_vector_indiv = []
        for num in red_range:
            f = red_features[num]
            red_feature_vector_indiv.extend([f['omission'], f['frequency'], f['heat']])
            red_feature_vector_indiv.extend(f['positional_frequency'])

        # 计算全局特征
        prev_red = sorted(prev_draw['red'])
        prev_red_sum = sum(prev_red)
        prev_red_odd_count = sum(1 for n in prev_red if n % 2 != 0)

        # 十位数分布
        tens_dist = [0] * 4
        for n in prev_red:
            if n < 10:
                tens_dist[0] += 1
            elif n < 20:
                tens_dist[1] += 1
            elif n < 30:
                tens_dist[2] += 1
            else:
                tens_dist[3] += 1

        # 间隔特征
        intervals = [prev_red[j + 1] - prev_red[j] for j in range(len(prev_red) - 1)]
        global_features = [
            prev_red_sum / 175, prev_red_odd_count / 5,
            np.mean(intervals) / 34, np.std(intervals) / 17
        ] + [c / 5 for c in tens_dist]

        final_red_feature_vector = red_feature_vector_indiv + global_features
        X_red.append(final_red_feature_vector)
        Y_red.append([1 if num in target_draw['red'] else 0 for num in red_range])

        # 构建蓝球特征向量
        blue_feature_vector = []
        for num in blue_range:
            f = blue_features[num]
            blue_feature_vector.extend([f['omission'], f['frequency'], f['heat']])

        X_blue.append(blue_feature_vector)
        Y_blue.append([1 if num in target_draw['blue'] else 0 for num in blue_range])

    print(f"✅ 数据集创建完成！")
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)


# 4. 定制化损失函数
def create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0):
    # 此内部函数的名字 'loss_fn' 在加载模型时需要用到
    def loss_fn(y_true, y_pred):
        bce = binary_crossentropy(y_true, y_pred)
        pred_sum = K.sum(y_pred, axis=-1)
        sum_error = K.square(pred_sum - target_sum)
        return bce + lambda_reg * sum_error

    return loss_fn


# 5. 模型结构与中奖检查
def create_dense_model(input_shape, output_shape, loss_function):
    # ... 此函数未改变 ...
    model = Sequential([
        Dense(256, activation='relu', input_shape=(input_shape,)),
        Dropout(0.4),
        Dense(128, activation='relu'),
        Dropout(0.4),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(output_shape, activation='sigmoid')
    ])
    model.compile(optimizer=Adam(learning_rate=0.0005), loss=loss_function, metrics=['accuracy'])
    return model


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    """检查预测结果的中奖等级"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    if red_hits == 5 and blue_hits == 2: return 1  # 一等奖
    if red_hits == 5 and blue_hits == 1: return 2  # 二等奖
    if red_hits == 5 and blue_hits == 0: return 3  # 三等奖
    if red_hits == 4 and blue_hits == 2: return 4  # 四等奖
    if red_hits == 4 and blue_hits == 1: return 5  # 五等奖
    if red_hits == 3 and blue_hits == 2: return 6  # 六等奖
    return 0  # 未中奖


def evaluate_model_performance(red_model, blue_model, X_red_test, Y_red_test, X_blue_test, Y_blue_test,
                             red_range, blue_range, num_attempts=10):
    """评估模型在指定次数内的命中性能"""
    total_hits = 0
    total_tests = len(X_red_test)

    for i in range(total_tests):
        actual_red = {red_range.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
        actual_blue = {blue_range.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

        # 获取模型预测概率
        red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
        blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

        # 标准化概率
        red_probs_normalized = red_probs / np.sum(red_probs)
        blue_probs_normalized = blue_probs / np.sum(blue_probs)

        # 在指定次数内尝试预测
        hit_in_attempts = False
        for attempt in range(num_attempts):
            # 使用概率采样生成预测
            pred_red_indices = np.random.choice(len(red_probs), size=5, replace=False, p=red_probs_normalized)
            pred_red = {red_range.start + idx for idx in pred_red_indices}
            pred_blue_indices = np.random.choice(len(blue_probs), size=2, replace=False, p=blue_probs_normalized)
            pred_blue = {blue_range.start + idx for idx in pred_blue_indices}

            prize = check_prize(pred_red, pred_blue, actual_red, actual_blue)
            if prize in [1, 2]:
               print(f"\n🎉🎉🎉 命中目标! 奖项: {prize}等奖! 🎉🎉🎉")
               hit_in_attempts = True
               break

        if hit_in_attempts:
            total_hits += 1

    hit_rate = total_hits / total_tests if total_tests > 0 else 0
    return hit_rate, total_hits, total_tests


def smart_predict_numbers(red_probs, blue_probs, red_range, blue_range):
    """智能预测号码，结合概率和策略"""
    # 获取高概率号码
    top_red_indices = np.argsort(red_probs)[-15:]  # 取前15个高概率红球
    top_blue_indices = np.argsort(blue_probs)[-6:]   # 取前6个高概率蓝球

    # 从高概率号码中随机选择
    selected_red_indices = np.random.choice(top_red_indices, size=5, replace=False)
    selected_blue_indices = np.random.choice(top_blue_indices, size=2, replace=False)

    pred_red = {red_range.start + idx for idx in selected_red_indices}
    pred_blue = {blue_range.start + idx for idx in selected_blue_indices}

    return pred_red, pred_blue


# --- 6. 主程序 ---
if __name__ == "__main__":
    print("🚀 程序启动...")
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)
    # --- 【新】定义模型保存路径 ---
    RED_MODEL_PATH = "red_ball_predictor.keras"
    BLUE_MODEL_PATH = "blue_ball_predictor.keras"

    print("--- 🎲 开始执行智能彩票预测程序 ---")
    print("📂 正在加载历史数据...")
    all_draws = load_all_draws()
    print(f"✅ 成功加载 {len(all_draws)} 期历史数据")

    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print(f"数据不足，需要至少 {FREQUENCY_WINDOW + NUM_TEST_PERIODS} 期历史数据。")
    else:
        # 数据准备和分割环节提前，无论加载还是训练都需要测试集
        print("正在创建数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_composite_dataset(
            all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
        )
        print("✅ 数据集创建完成。")
        X_red_train, Y_red_train = X_red_all[:-NUM_TEST_PERIODS], Y_red_all[:-NUM_TEST_PERIODS]
        X_blue_train, Y_blue_train = X_blue_all[:-NUM_TEST_PERIODS], Y_blue_all[:-NUM_TEST_PERIODS]
        X_red_test, Y_red_test = X_red_all[-NUM_TEST_PERIODS:], Y_red_all[-NUM_TEST_PERIODS:]
        X_blue_test, Y_blue_test = X_blue_all[-NUM_TEST_PERIODS:], Y_blue_all[-NUM_TEST_PERIODS:]

        # 准备自定义损失函数，加载和训练时都需要它
        red_loss = create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0)
        blue_loss = create_sum_regularized_loss(lambda_reg=0.05, target_sum=2.0)

        # --- 【新】智能模型训练与评估循环 ---
        def train_new_model():
            """训练新模型"""
            print("\n--- 🧠 开始训练新模型... ---")
            red_model = create_dense_model(X_red_train.shape[1], Y_red_train.shape[1], red_loss)
            blue_model = create_dense_model(X_blue_train.shape[1], Y_blue_train.shape[1], blue_loss)

            early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

            print("\n--- 🔴 开始训练红球模型 ---")
            red_model.fit(X_red_train, Y_red_train, epochs=10, batch_size=128, verbose=1, validation_split=0.2,
                          callbacks=[early_stopping])

            print("\n--- 🔵 开始训练蓝球模型 ---")
            blue_model.fit(X_blue_train, Y_blue_train, epochs=10, batch_size=128, verbose=1, validation_split=0.2,
                           callbacks=[early_stopping])

            return red_model, blue_model

        def save_models(red_model, blue_model):
            """保存模型"""
            print("\n--- 💾 正在保存模型到本地... ---")
            red_model.save(RED_MODEL_PATH)
            blue_model.save(BLUE_MODEL_PATH)
            print(f"✅ 模型已成功保存。")

        def delete_models():
            """删除现有模型文件"""
            if os.path.exists(RED_MODEL_PATH):
                os.remove(RED_MODEL_PATH)
                print(f"🗑️ 已删除 {RED_MODEL_PATH}")
            if os.path.exists(BLUE_MODEL_PATH):
                os.remove(BLUE_MODEL_PATH)
                print(f"🗑️ 已删除 {BLUE_MODEL_PATH}")

        # 模型训练与评估主循环
        red_model, blue_model = None, None
        model_attempt = 0

        while model_attempt < MAX_MODEL_RETRAIN_ATTEMPTS:
            model_attempt += 1
            print(f"\n{'='*60}")
            print(f"🎯 模型训练尝试 {model_attempt}/{MAX_MODEL_RETRAIN_ATTEMPTS}")
            print(f"{'='*60}")

            # 检查是否存在现有模型
            if model_attempt == 1 and os.path.exists(RED_MODEL_PATH) and os.path.exists(BLUE_MODEL_PATH):
                print(f"\n--- 🧠 发现本地模型，正在加载并评估... ---")
                try:
                    red_model = load_model(RED_MODEL_PATH, custom_objects={'loss_fn': red_loss})
                    blue_model = load_model(BLUE_MODEL_PATH, custom_objects={'loss_fn': blue_loss})
                    print("✅ 模型加载成功。")
                except Exception as e:
                    print(f"❌ 模型加载失败: {e}")
                    delete_models()
                    red_model, blue_model = train_new_model()
            else:
                # 删除旧模型并训练新模型
                if model_attempt > 1:
                    delete_models()
                red_model, blue_model = train_new_model()

            # 评估模型性能
            print(f"\n--- 📊 评估模型在{NUM_SIMULATION_ATTEMPTS}次内的命中性能 ---")
            hit_rate, total_hits, total_tests = evaluate_model_performance(
                red_model, blue_model, X_red_test, Y_red_test, X_blue_test, Y_blue_test,
                RED_BALL_RANGE, BLUE_BALL_RANGE, NUM_SIMULATION_ATTEMPTS
            )

            print(f"📈 模型性能评估结果:")
            print(f"   - 测试期数: {total_tests}")
            print(f"   - 命中期数: {total_hits}")
            print(f"   - 命中率: {hit_rate:.2%}")
            print(f"   - 目标命中率: {TARGET_HIT_RATE:.2%}")

            if hit_rate >= TARGET_HIT_RATE:
                print(f"🎉 模型性能达标！命中率 {hit_rate:.2%} >= 目标 {TARGET_HIT_RATE:.2%}")
                save_models(red_model, blue_model)
                break
            else:
                print(f"❌ 模型性能不达标。命中率 {hit_rate:.2%} < 目标 {TARGET_HIT_RATE:.2%}")
                if model_attempt < MAX_MODEL_RETRAIN_ATTEMPTS:
                    print(f"🔄 准备重新训练模型...")
                else:
                    print(f"⚠️ 已达到最大重训练次数，使用当前最佳模型。")
                    save_models(red_model, blue_model)

        if red_model is None or blue_model is None:
            print("❌ 模型训练失败，程序退出。")
            exit(1)

        # --- 智能回测与实战预测 ---
        print(f"\n--- 🎯 在最近 {NUM_TEST_PERIODS} 期数据上执行智能回测 ---")

        for i in range(NUM_TEST_PERIODS):
            actual_red = {RED_BALL_RANGE.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
            actual_blue = {BLUE_BALL_RANGE.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

            print("\n" + "=" * 60)
            print(f"回测第 {i + 1} 期 | 实际号码: 红{sorted(list(actual_red))} 蓝{sorted(list(actual_blue))}")
            print("-" * 60)

            red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
            blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

            # 显示高概率号码池
            top_15_red_indices = np.argsort(red_probs)[-15:]
            top_15_red = sorted([RED_BALL_RANGE.start + idx for idx in top_15_red_indices])
            top_15_prob_mass = np.sum(red_probs[top_15_red_indices])
            top_6_blue_indices = np.argsort(blue_probs)[-6:]
            top_6_blue = sorted([BLUE_BALL_RANGE.start + idx for idx in top_6_blue_indices])
            top_6_prob_mass = np.sum(blue_probs[top_6_blue_indices])

            print(f"⭐ 模型推荐高概率号码池:")
            print(f"   红球 (Top 15): {top_15_red}")
            print(f"   📈 模型信心: Top 15 红球占据了总概率的 {top_15_prob_mass:.2%}")
            print(f"   蓝球 (Top 6):  {top_6_blue}")
            print(f"   📈 模型信心: Top 6 蓝球占据了总概率的 {top_6_prob_mass:.2%}")
            print("-" * 60)

            # 执行智能预测（10次尝试）
            print(f"🎲 执行 {NUM_SIMULATION_ATTEMPTS} 次智能预测...")
            prize_counts = Counter()
            hit_found = False

            for attempt in range(NUM_SIMULATION_ATTEMPTS):
                pred_red, pred_blue = smart_predict_numbers(red_probs, blue_probs, RED_BALL_RANGE, BLUE_BALL_RANGE)
                prize = check_prize(pred_red, pred_blue, actual_red, actual_blue)

                print(f"   第{attempt+1}次预测: 红{sorted(list(pred_red))} 蓝{sorted(list(pred_blue))}", end="")

                if prize > 0:
                    prize_counts[prize] += 1
                    print(f" -> 🎉 命中{prize}等奖!")
                    hit_found = True
                else:
                    print(f" -> 未中奖")

            print("-" * 60)
            if hit_found:
                print(f"✅ 在 {NUM_SIMULATION_ATTEMPTS} 次预测中成功命中!")
                for p_level, count in sorted(prize_counts.items()):
                    print(f"   - {p_level}等奖: {count} 次")
            else:
                print(f"❌ 在 {NUM_SIMULATION_ATTEMPTS} 次预测中未能命中。")

        # --- 下期号码预测 ---
        print(f"\n{'='*60}")
        print("🔮 下期号码智能预测")
        print(f"{'='*60}")

        # 使用最新数据进行预测
        latest_red_features = X_red_all[-1:]
        latest_blue_features = X_blue_all[-1:]

        red_probs = red_model.predict(latest_red_features, verbose=0)[0]
        blue_probs = blue_model.predict(latest_blue_features, verbose=0)[0]

        print("📊 下期预测分析:")
        top_15_red_indices = np.argsort(red_probs)[-15:]
        top_15_red = sorted([RED_BALL_RANGE.start + idx for idx in top_15_red_indices])
        top_6_blue_indices = np.argsort(blue_probs)[-6:]
        top_6_blue = sorted([BLUE_BALL_RANGE.start + idx for idx in top_6_blue_indices])

        print(f"   推荐红球号码池: {top_15_red}")
        print(f"   推荐蓝球号码池: {top_6_blue}")
        print("\n🎯 智能推荐组合 (基于概率优化):")

        for i in range(3):  # 推荐3组号码
            pred_red, pred_blue = smart_predict_numbers(red_probs, blue_probs, RED_BALL_RANGE, BLUE_BALL_RANGE)
            print(f"   组合{i+1}: 红球{sorted(list(pred_red))} + 蓝球{sorted(list(pred_blue))}")

        print(f"\n💡 建议: 从推荐号码池中选择，或参考智能推荐组合进行投注。")

    print(f"\n\n程序总耗时: {(time.time() - start_time):.2f} 秒。")