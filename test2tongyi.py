import numpy as np
import csv
import time
import json
from collections import Counter, deque
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout
from tensorflow.keras.optimizers import Adam
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import tensorflow.keras.backend as K
from tensorflow.keras.losses import binary_crossentropy
from datetime import datetime

# --- 全局设置 ---
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3
NUM_SIMULATION_ATTEMPTS = 10000
MAX_MODEL_RETRAIN_ATTEMPTS = 40  # 最大重训练次数
TARGET_HIT_RATE = 0.1  # 目标命中率（10次内至少命中1次）

# --- 增量学习设置 ---
INITIAL_LEARNING_RATE = 0.0005  # 初始学习率
MIN_LEARNING_RATE = 0.00001     # 最小学习率
INCREMENTAL_EPOCHS = 5          # 每次增量训练的轮数
PATIENCE_EPOCHS = 3             # 早停耐心值
TRAINING_HISTORY_FILE = "training_history.json"  # 训练历史文件
BEST_MODEL_BACKUP_DIR = "model_backups"          # 最佳模型备份目录


# --- 增量学习辅助函数 ---

def load_training_history():
    """加载训练历史记录"""
    if os.path.exists(TRAINING_HISTORY_FILE):
        try:
            with open(TRAINING_HISTORY_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"⚠️ 加载训练历史失败: {e}")
    return {
        "iterations": [],
        "best_hit_rate": 0.0,
        "best_iteration": 0,
        "total_iterations": 0
    }

def save_training_history(history):
    """保存训练历史记录"""
    try:
        with open(TRAINING_HISTORY_FILE, 'w', encoding='utf-8') as f:
            json.dump(history, f, indent=2, ensure_ascii=False)
    except Exception as e:
        print(f"⚠️ 保存训练历史失败: {e}")

def create_model_backup(red_model, blue_model, iteration, hit_rate):
    """创建模型备份"""
    if not os.path.exists(BEST_MODEL_BACKUP_DIR):
        os.makedirs(BEST_MODEL_BACKUP_DIR)

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_red_path = os.path.join(BEST_MODEL_BACKUP_DIR, f"red_model_iter{iteration}_{timestamp}_rate{hit_rate:.3f}.keras")
    backup_blue_path = os.path.join(BEST_MODEL_BACKUP_DIR, f"blue_model_iter{iteration}_{timestamp}_rate{hit_rate:.3f}.keras")

    try:
        red_model.save(backup_red_path)
        blue_model.save(backup_blue_path)
        print(f"📦 模型备份已保存: 迭代{iteration}, 命中率{hit_rate:.3f}")
        return backup_red_path, backup_blue_path
    except Exception as e:
        print(f"⚠️ 模型备份失败: {e}")
        return None, None

def get_adaptive_learning_rate(current_hit_rate, best_hit_rate, base_lr):
    """根据性能自适应调整学习率"""
    if current_hit_rate >= best_hit_rate:
        # 性能提升，保持或略微降低学习率
        return max(base_lr * 0.9, MIN_LEARNING_RATE)
    else:
        # 性能下降，降低学习率
        return max(base_lr * 0.5, MIN_LEARNING_RATE)

# --- 所有函数定义 (与之前版本相同) ---

# 1. 数据加载
def load_all_draws(filename="lottery_results_formatted.csv"):
    # ... 此函数未改变 ...
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。请确保CSV文件在同一目录下。")
        return []
    return all_draws


# 2. 独立号码特征工程
def calculate_individual_features(history_draws, red_range, blue_range):
    """计算个体特征 - 优化版本"""
    red_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0,
        'positional_frequency': [0] * 5,
    } for num in red_range}
    blue_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0
    } for num in blue_range}

    if not history_draws:
        return red_features, blue_features

    # 优化：只计算最后出现位置
    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}

    # 反向遍历，找到最后出现位置
    for i in range(len(history_draws) - 1, -1, -1):
        draw = history_draws[i]
        for num in draw['red']:
            if last_seen_red[num] == -1:
                last_seen_red[num] = i
        for num in draw['blue']:
            if last_seen_blue[num] == -1:
                last_seen_blue[num] = i

    total_draws = len(history_draws)
    for num in red_range:
        red_features[num]['omission'] = total_draws - 1 - last_seen_red[num]
    for num in blue_range:
        blue_features[num]['omission'] = total_draws - 1 - last_seen_blue[num]

    # 只使用最近的窗口数据
    windowed_history = history_draws[-FREQUENCY_WINDOW:]
    red_counts, blue_counts = Counter(), Counter()
    positional_counts = {num: [0] * 5 for num in red_range}

    # 简化热度计算
    for draw in windowed_history:
        sorted_red = sorted(draw['red'])
        for idx, num in enumerate(sorted_red):
            positional_counts[num][idx] += 1

        # 直接计数，不用复杂的热度公式
        for num in draw['red']:
            red_counts[num] += 1
        for num in draw['blue']:
            blue_counts[num] += 1

    # 设置特征值
    for num in red_range:
        red_features[num]['frequency'] = red_counts[num]
        red_features[num]['heat'] = red_counts[num] / len(windowed_history)  # 简化热度
        red_features[num]['positional_frequency'] = positional_counts[num]

    for num in blue_range:
        blue_features[num]['frequency'] = blue_counts[num]
        blue_features[num]['heat'] = blue_counts[num] / len(windowed_history)  # 简化热度

    return red_features, blue_features


# 3. 融合宏观特征的数据集创建
def create_composite_dataset(all_draws, red_range, blue_range):
    """创建复合数据集 - 优化版本"""
    print(f"📊 开始创建数据集，总数据: {len(all_draws)} 期")
    X_red, Y_red, X_blue, Y_blue = [], [], [], []

    total_samples = len(all_draws) - FREQUENCY_WINDOW
    print(f"📊 将生成 {total_samples} 个训练样本")

    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        # 显示进度
        if (i - FREQUENCY_WINDOW) % 20 == 0:
            progress = (i - FREQUENCY_WINDOW + 1) / total_samples * 100
            print(f"   进度: {progress:.1f}% ({i - FREQUENCY_WINDOW + 1}/{total_samples})")

        history_for_features = all_draws[:i]
        target_draw, prev_draw = all_draws[i], all_draws[i - 1]
        red_features, blue_features = calculate_individual_features(history_for_features, red_range, blue_range)

        # 构建红球特征向量
        red_feature_vector_indiv = []
        for num in red_range:
            f = red_features[num]
            red_feature_vector_indiv.extend([f['omission'], f['frequency'], f['heat']])
            red_feature_vector_indiv.extend(f['positional_frequency'])

        # 计算全局特征
        prev_red = sorted(prev_draw['red'])
        prev_red_sum = sum(prev_red)
        prev_red_odd_count = sum(1 for n in prev_red if n % 2 != 0)

        # 十位数分布
        tens_dist = [0] * 4
        for n in prev_red:
            if n < 10:
                tens_dist[0] += 1
            elif n < 20:
                tens_dist[1] += 1
            elif n < 30:
                tens_dist[2] += 1
            else:
                tens_dist[3] += 1

        # 间隔特征
        intervals = [prev_red[j + 1] - prev_red[j] for j in range(len(prev_red) - 1)]
        global_features = [
            prev_red_sum / 175, prev_red_odd_count / 5,
            np.mean(intervals) / 34, np.std(intervals) / 17
        ] + [c / 5 for c in tens_dist]

        final_red_feature_vector = red_feature_vector_indiv + global_features
        X_red.append(final_red_feature_vector)
        Y_red.append([1 if num in target_draw['red'] else 0 for num in red_range])

        # 构建蓝球特征向量
        blue_feature_vector = []
        for num in blue_range:
            f = blue_features[num]
            blue_feature_vector.extend([f['omission'], f['frequency'], f['heat']])

        X_blue.append(blue_feature_vector)
        Y_blue.append([1 if num in target_draw['blue'] else 0 for num in blue_range])

    print(f"✅ 数据集创建完成！")
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)


# 4. 定制化损失函数
def create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0):
    # 此内部函数的名字 'loss_fn' 在加载模型时需要用到
    def loss_fn(y_true, y_pred):
        bce = binary_crossentropy(y_true, y_pred)
        pred_sum = K.sum(y_pred, axis=-1)
        sum_error = K.square(pred_sum - target_sum)
        return bce + lambda_reg * sum_error

    return loss_fn


# 5. 模型结构与中奖检查
def create_dense_model(input_shape, output_shape, loss_function, learning_rate=INITIAL_LEARNING_RATE):
    """创建深度神经网络模型，支持自定义学习率"""
    model = Sequential([
        Dense(256, activation='relu', input_shape=(input_shape,)),
        Dropout(0.4),
        Dense(128, activation='relu'),
        Dropout(0.4),
        Dense(64, activation='relu'),
        Dropout(0.3),
        Dense(output_shape, activation='sigmoid')
    ])
    model.compile(optimizer=Adam(learning_rate=learning_rate), loss=loss_function, metrics=['accuracy'])
    return model

def update_model_learning_rate(model, new_learning_rate):
    """更新模型的学习率"""
    try:
        # 新版本 TensorFlow/Keras 的学习率设置方式
        model.optimizer.learning_rate.assign(new_learning_rate)
        print(f"📈 学习率已更新为: {new_learning_rate:.6f}")
    except AttributeError:
        # 备用方法：重新编译模型
        print(f"📈 使用重新编译方式更新学习率: {new_learning_rate:.6f}")
        model.compile(
            optimizer=Adam(learning_rate=new_learning_rate),
            loss=model.loss,
            metrics=model.metrics
        )

def incremental_train_model(model, X_train, Y_train, learning_rate, epochs=INCREMENTAL_EPOCHS):
    """增量训练模型"""
    # 更新学习率
    update_model_learning_rate(model, learning_rate)

    # 设置回调函数
    callbacks = [
        EarlyStopping(monitor='val_loss', patience=PATIENCE_EPOCHS, restore_best_weights=True, verbose=1),
        ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=2, min_lr=MIN_LEARNING_RATE, verbose=1)
    ]

    # 增量训练
    print(f"🔄 开始增量训练 (学习率: {learning_rate:.6f}, 轮数: {epochs})")
    history = model.fit(
        X_train, Y_train,
        epochs=epochs,
        batch_size=128,
        verbose=1,
        validation_split=0.2,
        callbacks=callbacks
    )

    return history


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    """检查预测结果的中奖等级"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    if red_hits == 5 and blue_hits == 2: return 1  # 一等奖
    if red_hits == 5 and blue_hits == 1: return 2  # 二等奖
    if red_hits == 5 and blue_hits == 0: return 3  # 三等奖
    if red_hits == 4 and blue_hits == 2: return 4  # 四等奖
    if red_hits == 4 and blue_hits == 1: return 5  # 五等奖
    if red_hits == 3 and blue_hits == 2: return 6  # 六等奖
    return 0  # 未中奖


def evaluate_model_performance(red_model, blue_model, X_red_test, Y_red_test, X_blue_test, Y_blue_test,
                             red_range, blue_range, num_attempts=10):
    """评估模型在指定次数内的命中性能"""
    total_hits = 0
    total_tests = len(X_red_test)

    for i in range(total_tests):
        actual_red = {red_range.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
        actual_blue = {blue_range.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

        # 获取模型预测概率
        red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
        blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

        # 标准化概率
        red_probs_normalized = red_probs / np.sum(red_probs)
        blue_probs_normalized = blue_probs / np.sum(blue_probs)

        # 在指定次数内尝试预测
        hit_in_attempts = False
        for attempt in range(num_attempts):
            # 使用概率采样生成预测
            pred_red_indices = np.random.choice(len(red_probs), size=5, replace=False, p=red_probs_normalized)
            pred_red = {red_range.start + idx for idx in pred_red_indices}
            pred_blue_indices = np.random.choice(len(blue_probs), size=2, replace=False, p=blue_probs_normalized)
            pred_blue = {blue_range.start + idx for idx in pred_blue_indices}

            prize = check_prize(pred_red, pred_blue, actual_red, actual_blue)
            if prize in [1, 2]:
               print(f"\n🎉🎉🎉 命中目标! 奖项: {prize}等奖! 🎉🎉🎉")
               hit_in_attempts = True
               break

        if hit_in_attempts:
            total_hits += 1

    hit_rate = total_hits / total_tests if total_tests > 0 else 0
    return hit_rate, total_hits, total_tests


def smart_predict_numbers(red_probs, blue_probs, red_range, blue_range):
    """智能预测号码，结合概率和策略"""
    # 获取高概率号码
    top_red_indices = np.argsort(red_probs)[-15:]  # 取前15个高概率红球
    top_blue_indices = np.argsort(blue_probs)[-6:]   # 取前6个高概率蓝球

    # 从高概率号码中随机选择
    selected_red_indices = np.random.choice(top_red_indices, size=5, replace=False)
    selected_blue_indices = np.random.choice(top_blue_indices, size=2, replace=False)

    pred_red = {red_range.start + idx for idx in selected_red_indices}
    pred_blue = {blue_range.start + idx for idx in selected_blue_indices}

    return pred_red, pred_blue


# --- 6. 主程序 ---
if __name__ == "__main__":
    print("🚀 程序启动...")
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)
    # --- 【新】定义模型保存路径 ---
    RED_MODEL_PATH = "red_ball_predictor.keras"
    BLUE_MODEL_PATH = "blue_ball_predictor.keras"

    print("--- 🎲 开始执行智能彩票预测程序 ---")
    print("📂 正在加载历史数据...")
    all_draws = load_all_draws()
    print(f"✅ 成功加载 {len(all_draws)} 期历史数据")

    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print(f"数据不足，需要至少 {FREQUENCY_WINDOW + NUM_TEST_PERIODS} 期历史数据。")
    else:
        # 数据准备和分割环节提前，无论加载还是训练都需要测试集
        print("正在创建数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_composite_dataset(
            all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
        )
        print("✅ 数据集创建完成。")
        X_red_train, Y_red_train = X_red_all[:-NUM_TEST_PERIODS], Y_red_all[:-NUM_TEST_PERIODS]
        X_blue_train, Y_blue_train = X_blue_all[:-NUM_TEST_PERIODS], Y_blue_all[:-NUM_TEST_PERIODS]
        X_red_test, Y_red_test = X_red_all[-NUM_TEST_PERIODS:], Y_red_all[-NUM_TEST_PERIODS:]
        X_blue_test, Y_blue_test = X_blue_all[-NUM_TEST_PERIODS:], Y_blue_all[-NUM_TEST_PERIODS:]

        # 准备自定义损失函数，加载和训练时都需要它
        red_loss = create_sum_regularized_loss(lambda_reg=0.1, target_sum=5.0)
        blue_loss = create_sum_regularized_loss(lambda_reg=0.05, target_sum=2.0)

        # --- 【新】增量学习训练与评估循环 ---
        def train_new_model(learning_rate=INITIAL_LEARNING_RATE):
            """训练新模型"""
            print("\n--- 🧠 开始训练新模型... ---")
            red_model = create_dense_model(X_red_train.shape[1], Y_red_train.shape[1], red_loss, learning_rate)
            blue_model = create_dense_model(X_blue_train.shape[1], Y_blue_train.shape[1], blue_loss, learning_rate)

            early_stopping = EarlyStopping(monitor='val_loss', patience=10, restore_best_weights=True)

            print("\n--- 🔴 开始训练红球模型 ---")
            red_model.fit(X_red_train, Y_red_train, epochs=10, batch_size=128, verbose=1, validation_split=0.2,
                          callbacks=[early_stopping])

            print("\n--- 🔵 开始训练蓝球模型 ---")
            blue_model.fit(X_blue_train, Y_blue_train, epochs=10, batch_size=128, verbose=1, validation_split=0.2,
                           callbacks=[early_stopping])

            return red_model, blue_model

        def incremental_train_models(red_model, blue_model, learning_rate):
            """增量训练现有模型"""
            print(f"\n--- 🔄 开始增量训练 (学习率: {learning_rate:.6f}) ---")

            print("\n--- 🔴 增量训练红球模型 ---")
            red_history = incremental_train_model(red_model, X_red_train, Y_red_train, learning_rate)

            print("\n--- 🔵 增量训练蓝球模型 ---")
            blue_history = incremental_train_model(blue_model, X_blue_train, Y_blue_train, learning_rate)

            return red_history, blue_history

        def save_models(red_model, blue_model):
            """保存模型"""
            print("\n--- 💾 正在保存模型到本地... ---")
            red_model.save(RED_MODEL_PATH)
            blue_model.save(BLUE_MODEL_PATH)
            print(f"✅ 模型已成功保存。")

        def delete_models():
            """删除现有模型文件"""
            if os.path.exists(RED_MODEL_PATH):
                os.remove(RED_MODEL_PATH)
                print(f"🗑️ 已删除 {RED_MODEL_PATH}")
            if os.path.exists(BLUE_MODEL_PATH):
                os.remove(BLUE_MODEL_PATH)
                print(f"🗑️ 已删除 {BLUE_MODEL_PATH}")

        # --- 增量学习主循环 ---
        print("\n--- 📚 加载训练历史记录 ---")
        training_history = load_training_history()
        print(f"📊 历史记录: 总迭代{training_history['total_iterations']}次, 最佳命中率{training_history['best_hit_rate']:.3f}")

        red_model, blue_model = None, None
        current_learning_rate = INITIAL_LEARNING_RATE
        iteration = 0

        while iteration < MAX_MODEL_RETRAIN_ATTEMPTS:
            iteration += 1
            print(f"\n{'='*70}")
            print(f"🎯 增量学习迭代 {iteration}/{MAX_MODEL_RETRAIN_ATTEMPTS}")
            print(f"{'='*70}")

            # 第一次迭代：尝试加载现有模型或创建新模型
            if iteration == 1:
                if os.path.exists(RED_MODEL_PATH) and os.path.exists(BLUE_MODEL_PATH):
                    print(f"\n--- 🧠 发现本地模型，正在加载... ---")
                    try:
                        red_model = load_model(RED_MODEL_PATH, custom_objects={'loss_fn': red_loss})
                        blue_model = load_model(BLUE_MODEL_PATH, custom_objects={'loss_fn': blue_loss})
                        print("✅ 模型加载成功，将进行增量训练。")

                        # 获取当前学习率
                        try:
                            current_lr = float(red_model.optimizer.learning_rate.numpy())
                        except AttributeError:
                            current_lr = INITIAL_LEARNING_RATE
                        current_learning_rate = max(current_lr * 0.8, MIN_LEARNING_RATE)  # 略微降低学习率
                        print(f"📈 调整学习率: {current_lr:.6f} -> {current_learning_rate:.6f}")

                    except Exception as e:
                        print(f"❌ 模型加载失败: {e}")
                        delete_models()
                        print("🆕 创建新模型...")
                        red_model, blue_model = train_new_model(current_learning_rate)
                else:
                    print("🆕 未发现现有模型，创建新模型...")
                    red_model, blue_model = train_new_model(current_learning_rate)
            else:
                # 后续迭代：增量训练现有模型
                print(f"🔄 继续增量训练现有模型...")
                incremental_train_models(red_model, blue_model, current_learning_rate)

            # 评估模型性能
            print(f"\n--- 📊 评估模型性能 (迭代 {iteration}) ---")
            hit_rate, total_hits, total_tests = evaluate_model_performance(
                red_model, blue_model, X_red_test, Y_red_test, X_blue_test, Y_blue_test,
                RED_BALL_RANGE, BLUE_BALL_RANGE, NUM_SIMULATION_ATTEMPTS
            )

            # 记录本次迭代结果
            iteration_record = {
                "iteration": iteration,
                "hit_rate": hit_rate,
                "learning_rate": current_learning_rate,
                "timestamp": datetime.now().isoformat(),
                "total_hits": total_hits,
                "total_tests": total_tests
            }
            training_history["iterations"].append(iteration_record)
            training_history["total_iterations"] = iteration

            print(f"📈 迭代 {iteration} 性能评估结果:")
            print(f"   - 测试期数: {total_tests}")
            print(f"   - 命中期数: {total_hits}")
            print(f"   - 命中率: {hit_rate:.3f} ({hit_rate:.2%})")
            print(f"   - 学习率: {current_learning_rate:.6f}")
            print(f"   - 目标命中率: {TARGET_HIT_RATE:.2%}")

            # 检查是否为最佳性能
            if hit_rate > training_history["best_hit_rate"]:
                training_history["best_hit_rate"] = hit_rate
                training_history["best_iteration"] = iteration
                print(f"🎉 新的最佳性能！命中率提升至 {hit_rate:.3f}")

                # 创建最佳模型备份
                create_model_backup(red_model, blue_model, iteration, hit_rate)
                save_models(red_model, blue_model)

                # 性能提升，适当降低学习率以稳定训练
                current_learning_rate = get_adaptive_learning_rate(hit_rate, training_history["best_hit_rate"], current_learning_rate)

            else:
                print(f"📉 性能未提升 (最佳: {training_history['best_hit_rate']:.3f} 在迭代 {training_history['best_iteration']})")

                # 性能下降，降低学习率
                current_learning_rate = get_adaptive_learning_rate(hit_rate, training_history["best_hit_rate"], current_learning_rate)

            # 保存训练历史
            save_training_history(training_history)

            # 检查是否达到目标
            if hit_rate >= TARGET_HIT_RATE:
                print(f"🎯 目标达成！命中率 {hit_rate:.2%} >= 目标 {TARGET_HIT_RATE:.2%}")
                break

            # 检查学习率是否过低
            if current_learning_rate <= MIN_LEARNING_RATE:
                print(f"⚠️ 学习率已达到最小值 {MIN_LEARNING_RATE:.6f}，可能需要重新开始训练")
                if iteration < MAX_MODEL_RETRAIN_ATTEMPTS - 5:  # 还有足够迭代次数
                    print("🔄 重置学习率并继续训练...")
                    current_learning_rate = INITIAL_LEARNING_RATE * 0.5

        # 训练完成总结
        print(f"\n{'='*70}")
        print("📊 增量学习训练总结")
        print(f"{'='*70}")
        print(f"总迭代次数: {iteration}")
        print(f"最佳命中率: {training_history['best_hit_rate']:.3f} (迭代 {training_history['best_iteration']})")
        print(f"最终命中率: {hit_rate:.3f}")
        print(f"目标命中率: {TARGET_HIT_RATE:.3f}")

        if hit_rate >= TARGET_HIT_RATE:
            print("✅ 训练成功达到目标！")
        else:
            print("⚠️ 未达到目标，但已完成最大迭代次数。")

        if red_model is None or blue_model is None:
            print("❌ 模型训练失败，程序退出。")
            exit(1)

        # --- 智能回测与实战预测 ---
        print(f"\n--- 🎯 在最近 {NUM_TEST_PERIODS} 期数据上执行智能回测 ---")

        for i in range(NUM_TEST_PERIODS):
            actual_red = {RED_BALL_RANGE.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
            actual_blue = {BLUE_BALL_RANGE.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

            print("\n" + "=" * 60)
            print(f"回测第 {i + 1} 期 | 实际号码: 红{sorted(list(actual_red))} 蓝{sorted(list(actual_blue))}")
            print("-" * 60)

            red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
            blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

            # 显示高概率号码池
            top_15_red_indices = np.argsort(red_probs)[-15:]
            top_15_red = sorted([RED_BALL_RANGE.start + idx for idx in top_15_red_indices])
            top_15_prob_mass = np.sum(red_probs[top_15_red_indices])
            top_6_blue_indices = np.argsort(blue_probs)[-6:]
            top_6_blue = sorted([BLUE_BALL_RANGE.start + idx for idx in top_6_blue_indices])
            top_6_prob_mass = np.sum(blue_probs[top_6_blue_indices])

            print(f"⭐ 模型推荐高概率号码池:")
            print(f"   红球 (Top 15): {top_15_red}")
            print(f"   📈 模型信心: Top 15 红球占据了总概率的 {top_15_prob_mass:.2%}")
            print(f"   蓝球 (Top 6):  {top_6_blue}")
            print(f"   📈 模型信心: Top 6 蓝球占据了总概率的 {top_6_prob_mass:.2%}")
            print("-" * 60)

            # 执行智能预测（10次尝试）
            print(f"🎲 执行 {NUM_SIMULATION_ATTEMPTS} 次智能预测...")
            prize_counts = Counter()
            hit_found = False

            for attempt in range(NUM_SIMULATION_ATTEMPTS):
                pred_red, pred_blue = smart_predict_numbers(red_probs, blue_probs, RED_BALL_RANGE, BLUE_BALL_RANGE)
                prize = check_prize(pred_red, pred_blue, actual_red, actual_blue)

                print(f"   第{attempt+1}次预测: 红{sorted(list(pred_red))} 蓝{sorted(list(pred_blue))}", end="")

                if prize > 0:
                    prize_counts[prize] += 1
                    print(f" -> 🎉 命中{prize}等奖!")
                    hit_found = True
                else:
                    print(f" -> 未中奖")

            print("-" * 60)
            if hit_found:
                print(f"✅ 在 {NUM_SIMULATION_ATTEMPTS} 次预测中成功命中!")
                for p_level, count in sorted(prize_counts.items()):
                    print(f"   - {p_level}等奖: {count} 次")
            else:
                print(f"❌ 在 {NUM_SIMULATION_ATTEMPTS} 次预测中未能命中。")

        # --- 下期号码预测 ---
        print(f"\n{'='*60}")
        print("🔮 下期号码智能预测")
        print(f"{'='*60}")

        # 使用最新数据进行预测
        latest_red_features = X_red_all[-1:]
        latest_blue_features = X_blue_all[-1:]

        red_probs = red_model.predict(latest_red_features, verbose=0)[0]
        blue_probs = blue_model.predict(latest_blue_features, verbose=0)[0]

        print("📊 下期预测分析:")
        top_15_red_indices = np.argsort(red_probs)[-15:]
        top_15_red = sorted([RED_BALL_RANGE.start + idx for idx in top_15_red_indices])
        top_6_blue_indices = np.argsort(blue_probs)[-6:]
        top_6_blue = sorted([BLUE_BALL_RANGE.start + idx for idx in top_6_blue_indices])

        print(f"   推荐红球号码池: {top_15_red}")
        print(f"   推荐蓝球号码池: {top_6_blue}")
        print("\n🎯 智能推荐组合 (基于概率优化):")

        for i in range(3):  # 推荐3组号码
            pred_red, pred_blue = smart_predict_numbers(red_probs, blue_probs, RED_BALL_RANGE, BLUE_BALL_RANGE)
            print(f"   组合{i+1}: 红球{sorted(list(pred_red))} + 蓝球{sorted(list(pred_blue))}")

        print(f"\n💡 建议: 从推荐号码池中选择，或参考智能推荐组合进行投注。")

        # --- 训练历史可视化 ---
        print(f"\n{'='*70}")
        print("📈 训练历史回顾")
        print(f"{'='*70}")

        if training_history["iterations"]:
            print("迭代进度:")
            for i, record in enumerate(training_history["iterations"][-10:], 1):  # 显示最近10次迭代
                iteration_num = record["iteration"]
                hit_rate = record["hit_rate"]
                lr = record["learning_rate"]
                is_best = "🏆" if iteration_num == training_history["best_iteration"] else "  "
                print(f"  {is_best} 迭代 {iteration_num:2d}: 命中率 {hit_rate:.3f} | 学习率 {lr:.6f}")

            print(f"\n📊 训练统计:")
            print(f"   总迭代次数: {training_history['total_iterations']}")
            print(f"   最佳命中率: {training_history['best_hit_rate']:.3f} (迭代 {training_history['best_iteration']})")
            print(f"   目标命中率: {TARGET_HIT_RATE:.3f}")

            # 计算改进趋势
            if len(training_history["iterations"]) >= 2:
                first_rate = training_history["iterations"][0]["hit_rate"]
                last_rate = training_history["iterations"][-1]["hit_rate"]
                improvement = last_rate - first_rate
                print(f"   总体改进: {improvement:+.3f} ({improvement/first_rate*100:+.1f}%)")

    print(f"\n\n程序总耗时: {(time.time() - start_time):.2f} 秒。")