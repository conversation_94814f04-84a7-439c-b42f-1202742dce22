#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import csv
import time
from collections import Counter

# 全局设置
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3

def load_all_draws(filename="lottery_results_formatted.csv"):
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。")
        return []
    return all_draws

def calculate_individual_features(history_draws, red_range, blue_range):
    print(f"  计算特征，历史数据: {len(history_draws)} 期")
    red_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0,
        'positional_frequency': [0] * 5,
    } for num in red_range}
    blue_features = {num: {
        'omission': 0, 'frequency': 0, 'heat': 0.0
    } for num in blue_range}
    
    if not history_draws: 
        return red_features, blue_features
    
    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}
    
    for i, draw in enumerate(history_draws):
        for num in draw['red']: 
            last_seen_red[num] = i
        for num in draw['blue']: 
            last_seen_blue[num] = i
    
    total_draws = len(history_draws)
    for num in red_range: 
        red_features[num]['omission'] = total_draws - 1 - last_seen_red[num]
    for num in blue_range: 
        blue_features[num]['omission'] = total_draws - 1 - last_seen_blue[num]
    
    windowed_history = history_draws[-FREQUENCY_WINDOW:]
    red_counts, blue_counts = Counter(), Counter()
    positional_counts = {num: [0] * 5 for num in red_range}
    alpha = 0.1
    
    for draw in windowed_history:
        sorted_red = sorted(draw['red'])
        for idx, num in enumerate(sorted_red): 
            positional_counts[num][idx] += 1
        for num in red_range:
            is_present = 1 if num in draw['red'] else 0
            red_counts[num] += is_present
            red_features[num]['heat'] = alpha * is_present + (1 - alpha) * red_features[num]['heat']
        for num in blue_range:
            is_present = 1 if num in draw['blue'] else 0
            blue_counts[num] += is_present
            blue_features[num]['heat'] = alpha * is_present + (1 - alpha) * blue_features[num]['heat']
    
    for num in red_range:
        red_features[num]['frequency'] = red_counts[num]
        red_features[num]['positional_frequency'] = positional_counts[num]
    for num in blue_range: 
        blue_features[num]['frequency'] = blue_counts[num]
    
    return red_features, blue_features

def create_composite_dataset(all_draws, red_range, blue_range):
    print(f"开始创建数据集，总数据: {len(all_draws)} 期")
    X_red, Y_red, X_blue, Y_blue = [], [], [], []
    
    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        if i % 50 == 0:  # 每50期输出一次进度
            print(f"  处理进度: {i}/{len(all_draws)}")
        
        history_for_features = all_draws[:i]
        target_draw, prev_draw = all_draws[i], all_draws[i - 1]
        red_features, blue_features = calculate_individual_features(history_for_features, red_range, blue_range)
        
        red_feature_vector_indiv = []
        for num in red_range:
            f = red_features[num]
            red_feature_vector_indiv.extend([f['omission'], f['frequency'], f['heat']])
            red_feature_vector_indiv.extend(f['positional_frequency'])
        
        prev_red = sorted(prev_draw['red'])
        prev_red_sum = sum(prev_red)
        prev_red_odd_count = sum(1 for n in prev_red if n % 2 != 0)
        tens_dist = [0] * 4
        for n in prev_red:
            if n < 10:
                tens_dist[0] += 1
            elif n < 20:
                tens_dist[1] += 1
            elif n < 30:
                tens_dist[2] += 1
            else:
                tens_dist[3] += 1
        
        intervals = [prev_red[j + 1] - prev_red[j] for j in range(len(prev_red) - 1)]
        global_features = [
            prev_red_sum / 175, prev_red_odd_count / 5,
            np.mean(intervals) / 34, np.std(intervals) / 17
        ] + [c / 5 for c in tens_dist]
        
        final_red_feature_vector = red_feature_vector_indiv + global_features
        X_red.append(final_red_feature_vector)
        Y_red.append([1 if num in target_draw['red'] else 0 for num in red_range])
        
        blue_feature_vector = []
        for num in blue_range:
            f = blue_features[num]
            blue_feature_vector.extend([f['omission'], f['frequency'], f['heat']])
        
        X_blue.append(blue_feature_vector)
        Y_blue.append([1 if num in target_draw['blue'] else 0 for num in blue_range])
    
    print(f"数据集创建完成！")
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)

if __name__ == "__main__":
    print("🚀 测试数据集创建...")
    start_time = time.time()
    
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)
    
    print("📂 加载历史数据...")
    all_draws = load_all_draws()
    print(f"✅ 成功加载 {len(all_draws)} 期历史数据")
    
    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print(f"数据不足，需要至少 {FREQUENCY_WINDOW + NUM_TEST_PERIODS} 期历史数据。")
    else:
        print("🔧 开始创建数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_composite_dataset(
            all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
        )
        
        print(f"✅ 数据集创建完成！")
        print(f"   红球特征矩阵形状: {X_red_all.shape}")
        print(f"   红球标签矩阵形状: {Y_red_all.shape}")
        print(f"   蓝球特征矩阵形状: {X_blue_all.shape}")
        print(f"   蓝球标签矩阵形状: {Y_blue_all.shape}")
    
    print(f"\n总耗时: {(time.time() - start_time):.2f} 秒。")
