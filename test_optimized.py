import numpy as np
import csv
import time
from collections import Counter, deque
import os
import tensorflow as tf
from tensorflow.keras.models import Sequential, load_model
from tensorflow.keras.layers import Dense, Dropout, BatchNormalization
from tensorflow.keras.optimizers import Adam, RMSprop
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from tensorflow.keras.regularizers import l1_l2
import tensorflow.keras.backend as K
from tensorflow.keras.losses import binary_crossentropy

# --- 优化后的全局设置 ---
FREQUENCY_WINDOW = 100
NUM_TEST_PERIODS = 3

# --- 优化的训练参数 ---
TRAINING_CONFIG = {
    # 模型架构参数
    'red_model_layers': [256, 128, 64, 32],  # 红球模型层数配置
    'blue_model_layers': [128, 64, 32],      # 蓝球模型层数配置
    'dropout_rates': [0.4, 0.3, 0.2, 0.1],  # 各层Dropout率
    'use_batch_norm': True,                   # 是否使用批量归一化
    'l1_reg': 0.001,                         # L1正则化系数
    'l2_reg': 0.001,                         # L2正则化系数
    
    # 训练参数
    'learning_rate': 0.0005,                 # 初始学习率（降低以提高稳定性）
    'batch_size': 64,                        # 批次大小（减小以提高泛化）
    'epochs': 300,                           # 最大训练轮数
    'validation_split': 0.2,                 # 验证集比例
    
    # 回调函数参数
    'early_stopping_patience': 20,           # 早停耐心值
    'lr_reduce_patience': 10,                # 学习率降低耐心值
    'lr_reduce_factor': 0.5,                 # 学习率降低因子
    'min_lr': 1e-7,                         # 最小学习率
    
    # 优化器选择
    'optimizer_type': 'adam',                # 'adam' 或 'rmsprop'
    'beta_1': 0.9,                          # Adam优化器参数
    'beta_2': 0.999,                        # Adam优化器参数
}

# --- 自定义损失函数 ---
def create_weighted_loss(pos_weight=2.0):
    """创建加权二元交叉熵损失函数，提高正样本权重"""
    def weighted_binary_crossentropy(y_true, y_pred):
        # 计算基础二元交叉熵
        bce = binary_crossentropy(y_true, y_pred)
        
        # 为正样本添加权重
        weight_vector = y_true * pos_weight + (1 - y_true) * 1.0
        weighted_bce = bce * weight_vector
        
        return K.mean(weighted_bce)
    
    return weighted_bce

def create_focal_loss(alpha=0.25, gamma=2.0):
    """创建Focal Loss，解决类别不平衡问题"""
    def focal_loss(y_true, y_pred):
        # 防止数值不稳定
        epsilon = K.epsilon()
        y_pred = K.clip(y_pred, epsilon, 1.0 - epsilon)
        
        # 计算交叉熵
        ce = -y_true * K.log(y_pred)
        
        # 计算权重
        alpha_t = y_true * alpha + (1 - y_true) * (1 - alpha)
        p_t = y_true * y_pred + (1 - y_true) * (1 - y_pred)
        focal_weight = alpha_t * K.pow((1 - p_t), gamma)
        
        return K.mean(focal_weight * ce)
    
    return focal_loss

# --- 数据加载（保持不变） ---
def load_all_draws(filename="lottery_results_formatted.csv"):
    """加载所有历史开奖记录，并按时间正序（旧->新）返回。"""
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)
            for row in reader:
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。")
        return []
    return all_draws

# --- 增强的特征工程 ---
def calculate_enhanced_features(history_draws, red_range, blue_range):
    """计算增强的特征，包括更多统计信息"""
    red_features = {num: {
        'omission': 0, 'frequency': 0, 'heat_index': 0.0, 
        'trend': 0.0, 'variance': 0.0
    } for num in red_range}
    blue_features = {num: {
        'omission': 0, 'frequency': 0, 'heat_index': 0.0,
        'trend': 0.0, 'variance': 0.0
    } for num in blue_range}

    if not history_draws:
        return red_features, blue_features

    # 使用滑动窗口计算特征
    window_size = min(FREQUENCY_WINDOW, len(history_draws))
    recent_draws = history_draws[-window_size:]
    
    # 计算遗漏值
    last_seen_red = {num: -1 for num in red_range}
    last_seen_blue = {num: -1 for num in blue_range}
    
    for i, draw in enumerate(history_draws):
        for num in draw['red']:
            last_seen_red[num] = i
        for num in draw['blue']:
            last_seen_blue[num] = i
    
    # 计算各种特征
    for num in red_range:
        red_features[num]['omission'] = (len(history_draws) - 1) - last_seen_red[num]
        
        # 计算频率和热度指数
        appearances = [i for i, draw in enumerate(recent_draws) if num in draw['red']]
        red_features[num]['frequency'] = len(appearances)
        
        if appearances:
            # 热度指数：最近出现的权重更高
            weights = [1.0 / (len(recent_draws) - i) for i in appearances]
            red_features[num]['heat_index'] = sum(weights) / len(recent_draws)
            
            # 趋势：最近一半窗口 vs 前一半窗口的出现频率差
            mid_point = len(recent_draws) // 2
            recent_freq = sum(1 for i in appearances if i >= mid_point)
            early_freq = sum(1 for i in appearances if i < mid_point)
            red_features[num]['trend'] = (recent_freq - early_freq) / mid_point
            
            # 方差：出现间隔的方差
            if len(appearances) > 1:
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                red_features[num]['variance'] = np.var(intervals) if intervals else 0.0

    # 对蓝球进行相同处理
    for num in blue_range:
        blue_features[num]['omission'] = (len(history_draws) - 1) - last_seen_blue[num]
        
        appearances = [i for i, draw in enumerate(recent_draws) if num in draw['blue']]
        blue_features[num]['frequency'] = len(appearances)
        
        if appearances:
            weights = [1.0 / (len(recent_draws) - i) for i in appearances]
            blue_features[num]['heat_index'] = sum(weights) / len(recent_draws)
            
            mid_point = len(recent_draws) // 2
            recent_freq = sum(1 for i in appearances if i >= mid_point)
            early_freq = sum(1 for i in appearances if i < mid_point)
            blue_features[num]['trend'] = (recent_freq - early_freq) / mid_point
            
            if len(appearances) > 1:
                intervals = [appearances[i+1] - appearances[i] for i in range(len(appearances)-1)]
                blue_features[num]['variance'] = np.var(intervals) if intervals else 0.0

    return red_features, blue_features

# --- 创建增强的数据集 ---
def create_enhanced_dataset(all_draws, red_range, blue_range):
    """创建包含增强特征的数据集"""
    X_red, Y_red, X_blue, Y_blue = [], [], [], []
    
    print(f"正在创建增强特征数据集...")
    total_samples = len(all_draws) - FREQUENCY_WINDOW
    
    for i in range(FREQUENCY_WINDOW, len(all_draws)):
        if (i - FREQUENCY_WINDOW) % 50 == 0:
            progress = (i - FREQUENCY_WINDOW + 1) / total_samples * 100
            print(f"  进度: {progress:.1f}% ({i - FREQUENCY_WINDOW + 1}/{total_samples})")
            
        history = all_draws[:i]
        target_draw = all_draws[i]
        red_features, blue_features = calculate_enhanced_features(history, red_range, blue_range)

        # 构建特征向量（包含所有特征）
        red_feature_vector = []
        for num in red_range:
            features = red_features[num]
            red_feature_vector.extend([
                features['omission'], features['frequency'], features['heat_index'],
                features['trend'], features['variance']
            ])

        blue_feature_vector = []
        for num in blue_range:
            features = blue_features[num]
            blue_feature_vector.extend([
                features['omission'], features['frequency'], features['heat_index'],
                features['trend'], features['variance']
            ])

        # 标签向量
        red_label_vector = [1 if num in target_draw['red'] else 0 for num in red_range]
        blue_label_vector = [1 if num in target_draw['blue'] else 0 for num in blue_range]

        X_red.append(red_feature_vector)
        Y_red.append(red_label_vector)
        X_blue.append(blue_feature_vector)
        Y_blue.append(blue_label_vector)

    print("✅ 增强特征数据集创建完成。")
    return np.array(X_red), np.array(Y_red), np.array(X_blue), np.array(Y_blue)


# --- 优化的模型创建函数 ---
def create_optimized_model(input_shape, output_shape, model_type='red'):
    """创建优化的深度神经网络模型"""
    config = TRAINING_CONFIG

    # 根据模型类型选择层配置
    if model_type == 'red':
        layers = config['red_model_layers']
    else:
        layers = config['blue_model_layers']

    model = Sequential()

    # 输入层
    model.add(Dense(
        layers[0],
        activation='relu',
        input_shape=(input_shape,),
        kernel_regularizer=l1_l2(l1=config['l1_reg'], l2=config['l2_reg'])
    ))

    if config['use_batch_norm']:
        model.add(BatchNormalization())

    model.add(Dropout(config['dropout_rates'][0]))

    # 隐藏层
    for i, layer_size in enumerate(layers[1:], 1):
        model.add(Dense(
            layer_size,
            activation='relu',
            kernel_regularizer=l1_l2(l1=config['l1_reg'], l2=config['l2_reg'])
        ))

        if config['use_batch_norm']:
            model.add(BatchNormalization())

        if i < len(config['dropout_rates']):
            model.add(Dropout(config['dropout_rates'][i]))

    # 输出层
    model.add(Dense(output_shape, activation='sigmoid'))

    # 选择优化器
    if config['optimizer_type'] == 'adam':
        optimizer = Adam(
            learning_rate=config['learning_rate'],
            beta_1=config['beta_1'],
            beta_2=config['beta_2']
        )
    else:
        optimizer = RMSprop(learning_rate=config['learning_rate'])

    # 选择损失函数
    if model_type == 'red':
        loss_function = create_focal_loss(alpha=0.25, gamma=2.0)  # 红球使用Focal Loss
    else:
        loss_function = create_weighted_loss(pos_weight=3.0)      # 蓝球使用加权损失

    model.compile(
        optimizer=optimizer,
        loss=loss_function,
        metrics=['accuracy', 'precision', 'recall']
    )

    return model


def create_optimized_callbacks(model_path):
    """创建优化的回调函数"""
    config = TRAINING_CONFIG

    callbacks = [
        # 早停
        EarlyStopping(
            monitor='val_loss',
            patience=config['early_stopping_patience'],
            restore_best_weights=True,
            verbose=1
        ),

        # 学习率调度
        ReduceLROnPlateau(
            monitor='val_loss',
            factor=config['lr_reduce_factor'],
            patience=config['lr_reduce_patience'],
            min_lr=config['min_lr'],
            verbose=1
        ),

        # 模型检查点
        ModelCheckpoint(
            filepath=model_path,
            monitor='val_loss',
            save_best_only=True,
            save_weights_only=False,
            verbose=1
        )
    ]

    return callbacks


def train_optimized_model(model, X_train, Y_train, model_path, model_name):
    """使用优化参数训练模型"""
    config = TRAINING_CONFIG

    print(f"\n--- 🧠 开始训练{model_name}模型 (优化版) ---")
    print(f"模型架构: {[layer.units for layer in model.layers if hasattr(layer, 'units')]}")
    print(f"训练参数: 学习率={config['learning_rate']}, 批次大小={config['batch_size']}")
    print(f"正则化: L1={config['l1_reg']}, L2={config['l2_reg']}")

    callbacks = create_optimized_callbacks(model_path)

    history = model.fit(
        X_train, Y_train,
        epochs=config['epochs'],
        batch_size=config['batch_size'],
        validation_split=config['validation_split'],
        callbacks=callbacks,
        verbose=1
    )

    print(f"✅ {model_name}模型训练完成")
    return history


def check_prize(predicted_red, predicted_blue, actual_red, actual_blue):
    """核对中奖情况"""
    red_hits = len(set(predicted_red) & set(actual_red))
    blue_hits = len(set(predicted_blue) & set(actual_blue))
    prize = 0
    if red_hits == 5 and blue_hits == 2:
        prize = 1
    elif red_hits == 5 and blue_hits == 1:
        prize = 2
    return red_hits, blue_hits, prize


def smart_predict_with_strategy(red_probs, blue_probs, red_range, blue_range, strategy='balanced'):
    """智能预测策略"""
    if strategy == 'conservative':
        # 保守策略：选择概率最高的号码
        red_indices = np.argsort(red_probs)[-5:]
        blue_indices = np.argsort(blue_probs)[-2:]
    elif strategy == 'aggressive':
        # 激进策略：完全基于概率分布随机采样
        red_probs_norm = red_probs / np.sum(red_probs)
        blue_probs_norm = blue_probs / np.sum(blue_probs)
        red_indices = np.random.choice(len(red_probs), size=5, replace=False, p=red_probs_norm)
        blue_indices = np.random.choice(len(blue_probs), size=2, replace=False, p=blue_probs_norm)
    else:  # balanced
        # 平衡策略：从高概率号码中随机选择
        top_red_indices = np.argsort(red_probs)[-12:]  # 选择前12个高概率红球
        top_blue_indices = np.argsort(blue_probs)[-6:]   # 选择前6个高概率蓝球
        red_indices = np.random.choice(top_red_indices, size=5, replace=False)
        blue_indices = np.random.choice(top_blue_indices, size=2, replace=False)

    predicted_red = {red_range.start + idx for idx in red_indices}
    predicted_blue = {blue_range.start + idx for idx in blue_indices}

    return predicted_red, predicted_blue


# --- 主程序 ---
if __name__ == "__main__":
    start_time = time.time()
    RED_BALL_RANGE = range(1, 36)
    BLUE_BALL_RANGE = range(1, 13)

    # 优化版模型保存路径
    RED_MODEL_PATH = "optimized_red_model.keras"
    BLUE_MODEL_PATH = "optimized_blue_model.keras"

    print("=" * 70)
    print("🚀 开始执行【优化版】彩票预测程序")
    print("=" * 70)
    print(f"📊 训练配置:")
    print(f"   - 红球模型层数: {TRAINING_CONFIG['red_model_layers']}")
    print(f"   - 蓝球模型层数: {TRAINING_CONFIG['blue_model_layers']}")
    print(f"   - 学习率: {TRAINING_CONFIG['learning_rate']}")
    print(f"   - 批次大小: {TRAINING_CONFIG['batch_size']}")
    print(f"   - 最大训练轮数: {TRAINING_CONFIG['epochs']}")
    print(f"   - 使用批量归一化: {TRAINING_CONFIG['use_batch_norm']}")
    print(f"   - L1正则化: {TRAINING_CONFIG['l1_reg']}")
    print(f"   - L2正则化: {TRAINING_CONFIG['l2_reg']}")

    all_draws = load_all_draws()
    print(f"\n📂 成功加载 {len(all_draws)} 期历史数据")

    if len(all_draws) < FREQUENCY_WINDOW + NUM_TEST_PERIODS:
        print("❌ 数据不足，无法进行训练。")
    else:
        # 创建增强特征数据集
        print(f"\n📊 正在从 {len(all_draws)} 期历史数据中创建增强特征数据集...")
        X_red_all, Y_red_all, X_blue_all, Y_blue_all = create_enhanced_dataset(
            all_draws, RED_BALL_RANGE, BLUE_BALL_RANGE
        )

        # 数据分割
        X_red_train = X_red_all[:-NUM_TEST_PERIODS]
        Y_red_train = Y_red_all[:-NUM_TEST_PERIODS]
        X_blue_train = X_blue_all[:-NUM_TEST_PERIODS]
        Y_blue_train = Y_blue_all[:-NUM_TEST_PERIODS]

        X_red_test = X_red_all[-NUM_TEST_PERIODS:]
        Y_red_test = Y_red_all[-NUM_TEST_PERIODS:]
        X_blue_test = X_blue_all[-NUM_TEST_PERIODS:]
        Y_blue_test = Y_blue_all[-NUM_TEST_PERIODS:]

        print(f"\n📈 数据分割完成:")
        print(f"   - 训练集: {len(X_red_train)} 期")
        print(f"   - 测试集: {len(X_red_test)} 期")
        print(f"   - 红球特征维度: {X_red_train.shape[1]}")
        print(f"   - 蓝球特征维度: {X_blue_train.shape[1]}")

        # 模型训练或加载
        if os.path.exists(RED_MODEL_PATH) and os.path.exists(BLUE_MODEL_PATH):
            print(f"\n🧠 发现已训练的优化模型，正在加载...")
            try:
                red_model = load_model(RED_MODEL_PATH, compile=False)
                blue_model = load_model(BLUE_MODEL_PATH, compile=False)

                # 重新编译模型以确保使用正确的损失函数
                red_model.compile(
                    optimizer=Adam(learning_rate=TRAINING_CONFIG['learning_rate']),
                    loss=create_focal_loss(alpha=0.25, gamma=2.0),
                    metrics=['accuracy', 'precision', 'recall']
                )
                blue_model.compile(
                    optimizer=Adam(learning_rate=TRAINING_CONFIG['learning_rate']),
                    loss=create_weighted_loss(pos_weight=3.0),
                    metrics=['accuracy', 'precision', 'recall']
                )

                print("✅ 优化模型加载成功，跳过训练。")
            except Exception as e:
                print(f"❌ 模型加载失败: {e}")
                print("🔄 将重新训练模型...")
                os.remove(RED_MODEL_PATH) if os.path.exists(RED_MODEL_PATH) else None
                os.remove(BLUE_MODEL_PATH) if os.path.exists(BLUE_MODEL_PATH) else None

                red_model = create_optimized_model(X_red_train.shape[1], Y_red_train.shape[1], 'red')
                blue_model = create_optimized_model(X_blue_train.shape[1], Y_blue_train.shape[1], 'blue')

                train_optimized_model(red_model, X_red_train, Y_red_train, RED_MODEL_PATH, "红球")
                train_optimized_model(blue_model, X_blue_train, Y_blue_train, BLUE_MODEL_PATH, "蓝球")
        else:
            print(f"\n🧠 未发现优化模型，开始训练...")

            red_model = create_optimized_model(X_red_train.shape[1], Y_red_train.shape[1], 'red')
            blue_model = create_optimized_model(X_blue_train.shape[1], Y_blue_train.shape[1], 'blue')

            train_optimized_model(red_model, X_red_train, Y_red_train, RED_MODEL_PATH, "红球")
            train_optimized_model(blue_model, X_blue_train, Y_blue_train, BLUE_MODEL_PATH, "蓝球")

        # 智能回测
        print(f"\n" + "=" * 70)
        print(f"🎯 在最近 {NUM_TEST_PERIODS} 期数据上执行智能回测")
        print("=" * 70)

        strategies = ['conservative', 'balanced', 'aggressive']

        for i in range(NUM_TEST_PERIODS):
            actual_red = {RED_BALL_RANGE.start + j for j, label in enumerate(Y_red_test[i]) if label == 1}
            actual_blue = {BLUE_BALL_RANGE.start + j for j, label in enumerate(Y_blue_test[i]) if label == 1}

            print(f"\n📅 回测第 {i + 1} 期")
            print(f"🎯 实际号码: 红球{sorted(list(actual_red))} 蓝球{sorted(list(actual_blue))}")
            print("-" * 50)

            red_probs = red_model.predict(X_red_test[i:i + 1], verbose=0)[0]
            blue_probs = blue_model.predict(X_blue_test[i:i + 1], verbose=0)[0]

            # 显示概率分析
            top_5_red_indices = np.argsort(red_probs)[-5:]
            top_5_red = [RED_BALL_RANGE.start + idx for idx in top_5_red_indices]
            top_2_blue_indices = np.argsort(blue_probs)[-2:]
            top_2_blue = [BLUE_BALL_RANGE.start + idx for idx in top_2_blue_indices]

            print(f"📊 模型预测分析:")
            print(f"   - 红球Top5概率: {sorted(top_5_red)} (概率: {red_probs[top_5_red_indices].sum():.3f})")
            print(f"   - 蓝球Top2概率: {sorted(top_2_blue)} (概率: {blue_probs[top_2_blue_indices].sum():.3f})")

            # 使用不同策略进行预测
            for strategy in strategies:
                print(f"\n🎲 使用{strategy}策略预测:")
                attempts = 0
                max_attempts = 50000

                while attempts < max_attempts:
                    attempts += 1
                    predicted_red, predicted_blue = smart_predict_with_strategy(
                        red_probs, blue_probs, RED_BALL_RANGE, BLUE_BALL_RANGE, strategy
                    )

                    rh, bh, pz = check_prize(predicted_red, predicted_blue, actual_red, actual_blue)

                    if pz in [1, 2]:
                        print(f"   🎉 {strategy}策略命中 {pz}等奖！")
                        print(f"   📍 预测号码: 红球{sorted(list(predicted_red))} 蓝球{sorted(list(predicted_blue))}")
                        print(f"   🎯 命中结果: {rh}红球 + {bh}蓝球")
                        print(f"   🔢 尝试次数: {attempts:,}")
                        break

                    if attempts % 10000 == 0:
                        print(f"   ⏳ {strategy}策略已尝试 {attempts:,} 次...")

                if attempts >= max_attempts:
                    print(f"   ❌ {strategy}策略在 {max_attempts:,} 次尝试内未命中目标")

        print(f"\n" + "=" * 70)
        print("📈 优化效果总结")
        print("=" * 70)
        print("✅ 使用了以下优化技术:")
        print("   - 增强特征工程 (遗漏、频率、热度、趋势、方差)")
        print("   - 深度神经网络架构")
        print("   - 批量归一化和Dropout正则化")
        print("   - 自定义损失函数 (Focal Loss + 加权损失)")
        print("   - 智能学习率调度")
        print("   - 多种预测策略")
        print("   - L1/L2正则化防止过拟合")

    print(f"\n⏱️ 程序总耗时: {(time.time() - start_time):.2f} 秒")
    print("🎉 优化版彩票预测程序执行完成！")
