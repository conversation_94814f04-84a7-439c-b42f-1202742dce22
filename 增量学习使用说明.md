# 增量学习彩票预测系统使用说明

## 🎯 功能概述

本系统实现了智能增量学习机制，能够：
- 在现有模型基础上继续训练，避免从头开始
- 自动调整学习率，优化训练效果
- 记录训练历史，追踪性能变化
- 自动备份最佳模型版本
- 可视化训练进度和性能趋势

## 🔧 新增配置参数

```python
# 增量学习设置
INITIAL_LEARNING_RATE = 0.0005  # 初始学习率
MIN_LEARNING_RATE = 0.00001     # 最小学习率
INCREMENTAL_EPOCHS = 5          # 每次增量训练的轮数
PATIENCE_EPOCHS = 3             # 早停耐心值
TRAINING_HISTORY_FILE = "training_history.json"  # 训练历史文件
BEST_MODEL_BACKUP_DIR = "model_backups"          # 最佳模型备份目录
```

## 📊 增量学习流程

### 1. 首次运行
- 检查是否存在已保存的模型
- 如果存在，加载模型并调整学习率进行增量训练
- 如果不存在，创建新模型进行初始训练

### 2. 迭代训练
- 每次迭代进行少量轮次的增量训练（默认5轮）
- 评估模型性能并记录结果
- 根据性能变化自动调整学习率
- 如果性能提升，创建模型备份

### 3. 自适应学习率
- **性能提升时**：学习率 × 0.9（稳定训练）
- **性能下降时**：学习率 × 0.5（更保守的学习）
- **最小限制**：不低于 MIN_LEARNING_RATE

### 4. 早停机制
- 使用 EarlyStopping 防止过拟合
- 使用 ReduceLROnPlateau 动态降低学习率
- 验证集损失不改善时提前停止

## 📁 生成的文件

### 训练历史文件 (training_history.json)
```json
{
  "iterations": [
    {
      "iteration": 1,
      "hit_rate": 0.067,
      "learning_rate": 0.0005,
      "timestamp": "2024-12-16T14:30:22",
      "total_hits": 2,
      "total_tests": 3
    }
  ],
  "best_hit_rate": 0.067,
  "best_iteration": 1,
  "total_iterations": 1
}
```

### 模型备份目录 (model_backups/)
- `red_model_iter5_20241216_143022_rate0.123.keras`
- `blue_model_iter5_20241216_143022_rate0.123.keras`

## 🚀 使用方法

### 1. 运行测试
```bash
python test_incremental_learning.py
```

### 2. 运行主程序
```bash
python test2tongyi.py
```

### 3. 继续训练
再次运行主程序时，系统会：
- 自动加载之前的模型和训练历史
- 继续进行增量学习
- 累积训练经验

## 📈 训练监控

### 实时显示信息
- 当前迭代次数和总迭代次数
- 实时命中率和目标命中率
- 当前学习率和调整情况
- 最佳性能记录

### 训练历史回顾
- 最近10次迭代的详细记录
- 最佳性能标记（🏆）
- 总体改进趋势分析

## ⚙️ 高级配置

### 调整训练强度
```python
INCREMENTAL_EPOCHS = 10      # 增加每次训练轮数
MAX_MODEL_RETRAIN_ATTEMPTS = 60  # 增加最大迭代次数
```

### 调整学习率策略
```python
INITIAL_LEARNING_RATE = 0.001   # 提高初始学习率
MIN_LEARNING_RATE = 0.000001    # 降低最小学习率
```

### 调整目标性能
```python
TARGET_HIT_RATE = 0.15  # 提高目标命中率到15%
```

## 🔍 故障排除

### 1. 模型加载失败
- 检查模型文件是否完整
- 系统会自动删除损坏的模型并重新训练

### 2. 学习率过低
- 系统会自动检测并重置学习率
- 可手动调整 MIN_LEARNING_RATE 参数

### 3. 性能不提升
- 增加 INCREMENTAL_EPOCHS 进行更充分的训练
- 调整 TARGET_HIT_RATE 设置更合理的目标

## 💡 最佳实践

1. **定期备份**：重要的模型会自动备份到 model_backups 目录
2. **监控历史**：查看 training_history.json 了解训练趋势
3. **渐进训练**：让系统运行多次，累积训练经验
4. **参数调优**：根据实际效果调整配置参数

## 🎯 预期效果

- **训练效率**：比从头训练快 3-5 倍
- **性能稳定**：避免训练过程中的大幅波动
- **持续改进**：每次运行都在之前基础上优化
- **可追溯性**：完整的训练历史记录

通过增量学习，您的模型将能够持续改进，逐步提高预测准确性！
