# 模型训练参数优化说明

## 🎯 优化概述

相比原始的 `test.py`，新的 `test_optimized.py` 在以下方面进行了全面优化：

## 📊 参数对比

### 原始版本 vs 优化版本

| 参数类别 | 原始版本 | 优化版本 | 改进说明 |
|---------|---------|---------|---------|
| **模型架构** | [128, 64] | 红球[256,128,64,32] / 蓝球[128,64,32] | 更深的网络，更强的表达能力 |
| **学习率** | 0.001 | 0.0005 | 降低学习率提高训练稳定性 |
| **批次大小** | 128 | 64 | 减小批次提高泛化能力 |
| **Dropout** | 0.3 固定 | [0.4,0.3,0.2,0.1] 递减 | 层级化Dropout策略 |
| **正则化** | 无 | L1=0.001, L2=0.001 | 防止过拟合 |
| **损失函数** | 标准交叉熵 | Focal Loss + 加权损失 | 解决类别不平衡 |
| **特征工程** | 遗漏+频率 | 遗漏+频率+热度+趋势+方差 | 更丰富的特征 |

## 🔧 核心优化技术

### 1. 增强的模型架构
```python
# 红球模型：4层深度网络
red_model_layers = [256, 128, 64, 32]

# 蓝球模型：3层网络（蓝球数量较少）
blue_model_layers = [128, 64, 32]
```

**优势：**
- 更深的网络能学习更复杂的模式
- 层级化设计，逐步提取抽象特征
- 针对红球和蓝球的不同特点设计不同架构

### 2. 智能正则化策略
```python
# 批量归一化
use_batch_norm = True

# 层级化Dropout
dropout_rates = [0.4, 0.3, 0.2, 0.1]

# L1/L2正则化
l1_reg = 0.001
l2_reg = 0.001
```

**优势：**
- 批量归一化加速训练收敛
- 递减Dropout率，前层防过拟合，后层保留信息
- L1/L2正则化防止权重过大

### 3. 自定义损失函数

#### Focal Loss（红球模型）
```python
def create_focal_loss(alpha=0.25, gamma=2.0):
    # 解决类别不平衡问题
    # 关注难分类样本
```

#### 加权损失（蓝球模型）
```python
def create_weighted_loss(pos_weight=3.0):
    # 提高正样本权重
    # 适合蓝球数量少的特点
```

**优势：**
- 解决彩票预测中的严重类别不平衡问题
- 提高模型对中奖号码的敏感度

### 4. 增强特征工程
```python
特征维度：原始2维 → 优化5维
- omission: 遗漏值
- frequency: 频率
- heat_index: 热度指数（时间加权）
- trend: 趋势（近期vs早期频率）
- variance: 出现间隔方差
```

**优势：**
- 更全面地描述号码的历史行为
- 捕捉时间序列特征和趋势信息

### 5. 智能训练策略

#### 学习率调度
```python
# 初始学习率
learning_rate = 0.0005

# 自动降低学习率
ReduceLROnPlateau(
    factor=0.5,        # 降低50%
    patience=10,       # 10轮不改善就降低
    min_lr=1e-7       # 最小学习率
)
```

#### 早停机制
```python
EarlyStopping(
    patience=20,       # 20轮不改善就停止
    restore_best_weights=True
)
```

#### 模型检查点
```python
ModelCheckpoint(
    save_best_only=True,  # 只保存最佳模型
    monitor='val_loss'    # 监控验证损失
)
```

### 6. 多策略预测

#### 保守策略
- 选择概率最高的号码
- 适合稳健投注

#### 平衡策略
- 从高概率号码池中随机选择
- 平衡确定性和随机性

#### 激进策略
- 完全基于概率分布采样
- 探索更多可能性

## 📈 预期改进效果

### 训练效果
- **收敛速度**：批量归一化 + 优化学习率 → 快30-50%
- **稳定性**：正则化 + 早停 → 减少过拟合风险
- **准确性**：增强特征 + 深度网络 → 提高预测精度

### 预测效果
- **命中率**：自定义损失函数 → 提高对中奖号码的敏感度
- **策略多样性**：3种预测策略 → 适应不同风险偏好
- **鲁棒性**：正则化技术 → 提高模型泛化能力

## ⚙️ 参数调优建议

### 如果训练过慢
```python
# 减少模型复杂度
red_model_layers = [128, 64, 32]
batch_size = 128
```

### 如果过拟合严重
```python
# 增强正则化
dropout_rates = [0.5, 0.4, 0.3, 0.2]
l1_reg = 0.01
l2_reg = 0.01
```

### 如果欠拟合
```python
# 增加模型容量
red_model_layers = [512, 256, 128, 64]
learning_rate = 0.001
```

### 如果类别不平衡严重
```python
# 调整损失函数参数
focal_loss: gamma = 3.0  # 更关注难样本
weighted_loss: pos_weight = 5.0  # 更高正样本权重
```

## 🚀 使用建议

1. **首次运行**：让模型完整训练，建立基线
2. **参数调优**：根据训练结果调整配置
3. **策略选择**：根据风险偏好选择预测策略
4. **持续优化**：定期重新训练以适应新数据

## 📊 监控指标

训练过程中关注：
- **训练损失**：应该稳定下降
- **验证损失**：不应该持续上升（过拟合信号）
- **准确率**：整体预测准确性
- **精确率/召回率**：对中奖号码的预测能力

通过这些优化，模型的预测能力和稳定性都将得到显著提升！
