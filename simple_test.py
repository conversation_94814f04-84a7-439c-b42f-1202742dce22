#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import csv
import time

def load_all_draws(filename="lottery_results_formatted.csv"):
    print(f"开始加载文件: {filename}")
    all_draws = []
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            next(reader)  # 跳过表头
            count = 0
            for row in reader:
                count += 1
                if count % 50 == 0:
                    print(f"  已读取 {count} 行")
                parts = row[1].split(' | ')
                if len(parts) == 2 and parts[0] and parts[1]:
                    draw = {
                        "date": row[0],
                        "red": [int(n) for n in parts[0].split('-')],
                        "blue": [int(n) for n in parts[1].split('-')]
                    }
                    all_draws.append(draw)
    except FileNotFoundError:
        print(f"错误: 文件 '{filename}' 未找到。")
        return []
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return []
    
    print(f"文件读取完成，共 {len(all_draws)} 期数据")
    return all_draws

if __name__ == "__main__":
    print("🚀 简单测试程序启动...")
    start_time = time.time()
    
    all_draws = load_all_draws()
    
    if len(all_draws) > 0:
        print(f"✅ 数据加载成功！")
        print(f"   总期数: {len(all_draws)}")
        print(f"   最早期: {all_draws[0]}")
        print(f"   最新期: {all_draws[-1]}")
        
        # 简单统计
        red_numbers = []
        blue_numbers = []
        for draw in all_draws:
            red_numbers.extend(draw['red'])
            blue_numbers.extend(draw['blue'])
        
        print(f"   红球总数: {len(red_numbers)}")
        print(f"   蓝球总数: {len(blue_numbers)}")
        print(f"   红球范围: {min(red_numbers)} - {max(red_numbers)}")
        print(f"   蓝球范围: {min(blue_numbers)} - {max(blue_numbers)}")
    else:
        print("❌ 数据加载失败！")
    
    print(f"\n总耗时: {(time.time() - start_time):.2f} 秒。")
    print("程序结束。")
